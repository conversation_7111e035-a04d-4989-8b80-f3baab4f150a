# Performance Module Permissions

This document outlines the permission structure for the Performance module, implementing role-based access control for different user types.

## Permission Codes

| Permission Code | Permission Name              | Description                                                                                                  |
| --------------- | ---------------------------- | ------------------------------------------------------------------------------------------------------------ |
| 7113            | VIEW_INVENTORY_MARKETER      | Allows marketer users to view performance module (My Targets, My Commissions, My Cash on Cash, My Portfolio) |
| 7114            | VIEW_INVENTORY_GM_HQ         | Allows GM HQ users to view performance module (Marketer Performance, Marketer Portfolio)                     |
| 7115            | VIEW_INVENTORY_GM_KAREN      | Allows GM KAREN users to view performance module (Marketer Performance, Marketer Portfolio)                  |
| 7127            | VIEW_PERFORMANCE_TEAM_LEADER | Allows team leaders to view all marketer sections and marketer performance                                   |

## Role-Based Access Control

### Marketer (Permission 7113)

**Sections Visible:**

- My Targets (`/marketer-targets`)
- My Commissions (`/marketer-commissions`)
- My Cash on Cash (`/cash-on-cash`)
- My Portfolio (`/portfolio`)

### GM Karen (Permission 7115) AND GM HQ (Permission 7114)

**Sections Visible:**

- Marketer Performance (`/performance`)
- Marketer Portfolio (`/portfolio`)

### Team Leaders (Permission 7127)

**Sections Visible:**

- ALL sections from Marketers above
- Marketer Performance (`/performance`)
- Office Performance (`/office-performance`)
- Targets (`/targets`)
- Commissions (`/commissions`)
- Team Performance (`/team-performance`)
- Profiles (`/profiles`)

## Implementation Details

### 1. Permission Constants

Added `PERFORMANCE_PERMISSIONS` constant in `src/hooks/useSidebarPermissions.ts`:

```typescript
export const PERFORMANCE_PERMISSIONS = {
  VIEW_PERFORMANCE_MARKETER: 7113, // Same as VIEW_INVENTORY_MARKETER
  VIEW_PERFORMANCE_GM_HQ: 7114, // Same as VIEW_INVENTORY_GM_HQ
  VIEW_PERFORMANCE_GM_KAREN: 7115, // Same as VIEW_INVENTORY_GM_KAREN
  VIEW_PERFORMANCE_TEAM_LEADER: 7127, // New permission for team leaders
} as const;
```

### 2. Permission Descriptions

Updated `src/utils/permissionChecker.ts` with descriptions for performance permissions:

```typescript
7113: "View Inventory Marketer - Allows marketer users to view and access my booking section and performance module (My Targets, My Commissions, My Cash on Cash, My Portfolio)",
7114: "View Inventory GM HQ - Allows GM HQ users to view and access special bookings section and performance module (Marketer Performance, Marketer Portfolio)",
7115: "View Inventory GM KAREN - Allows GM KAREN users to view and access special bookings section and performance module (Marketer Performance, Marketer Portfolio)",
7123: "View Performance Team Leader - Allows team leaders to view all marketer sections and marketer performance in performance module",
```

### 3. Sidebar Filtering

Implemented `filterPerformanceItems` function in `src/app-components/sidebar/app-sidebar.tsx`:

```typescript
const filterPerformanceItems = (items: any[]) => {
  return items.filter((item) => {
    switch (item.title) {
      // Marketer sections - only visible to marketers (7113)
      case "Marketer Targets":
      case "Marketer Commissions":
      case "Cash On Cash":
      case "Portfolio":
        return hasPerformancePermission("VIEW_PERFORMANCE_MARKETER");

      // GM sections - visible to GM HQ (7114) and GM Karen (7115)
      case "Marketer Performance":
        return (
          hasPerformancePermission("VIEW_PERFORMANCE_GM_HQ") ||
          hasPerformancePermission("VIEW_PERFORMANCE_GM_KAREN") ||
          hasPerformancePermission("VIEW_PERFORMANCE_TEAM_LEADER")
        );

      // Team Leader sections - visible to team leaders (7123) and above
      case "Office Performance":
      case "Targets":
      case "Commissions":
      case "Team Performance":
      case "Profiles":
        return (
          hasPerformancePermission("VIEW_PERFORMANCE_TEAM_LEADER") ||
          hasPerformancePermission("VIEW_PERFORMANCE_GM_HQ") ||
          hasPerformancePermission("VIEW_PERFORMANCE_GM_KAREN")
        );

      default:
        return false; // Hide items that don't match any permission
    }
  });
};
```

### 4. Route Protection

Updated route permissions in:

- `src/components/auth/PermissionGuard.tsx`
- `src/hooks/usePermissionNavigation.ts`
- `src/components/navigation/PermissionAwareLink.tsx`

```typescript
// Performance routes with role-based permissions
'/performance': [7114, 7115, 7127], // Marketer Performance - GM HQ, GM Karen, Team Leader
'/marketer-targets': [7113], // My Targets - Marketer only
'/marketer-commissions': [7113], // My Commissions - Marketer only
'/cash-on-cash': [7113], // My Cash on Cash - Marketer only
'/portfolio': [7113, 7114, 7115], // My Portfolio (marketer) + Marketer Portfolio (GMs)
'/office-performance': [7127, 7114, 7115], // Team Leader and above
'/targets': [7127, 7114, 7115], // Team Leader and above
'/commissions': [7127, 7114, 7115], // Team Leader and above
'/team-performance': [7127, 7114, 7115], // Team Leader and above
'/profiles': [7127, 7114, 7115], // Team Leader and above
```

## Permission Assignment

To assign these permissions to users:

1. **Marketers**: Assign permission 7113
2. **GM Karen**: Assign permission 7115
3. **GM HQ**: Assign permission 7114
4. **Team Leaders**: Assign permission 7127

## Testing

To test the implementation:

1. Create test users with different permission combinations
2. Verify that each role sees only the appropriate performance sections
3. Test route access by navigating directly to URLs
4. Confirm that unauthorized users are redirected or see permission errors

## Notes

- Permission 7127 is a new permission specifically created for team leaders
- Permissions 7113, 7114, and 7115 are reused from the inventory module
- The implementation maintains backward compatibility with existing permission structures
- Team leaders have the most comprehensive access, seeing all sections available to lower roles plus additional management sections
