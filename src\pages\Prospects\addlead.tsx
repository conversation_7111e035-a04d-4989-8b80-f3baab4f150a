// src/components/AddProspects.tsx
import { useState, useEffect } from "react";
import { useForm } from "react-hook-form";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Select,
  SelectContent,
  SelectGroup,
  SelectItem,
  SelectLabel,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import Multiselect from "@/components/custom/forms/Multiselect";
import {
  useAddProspectMutation,
  useGetLeadSourceQuery,
  useLazyGetLeadSourceQuery,
} from "@/redux/slices/propects";
import { toast } from "sonner";
import BaseModal from "@/components/custom/modals/BaseModal";
import { ActionButton } from "@/components/custom/buttons/buttons";
import { ChevronLeft, ChevronRight, Send } from "lucide-react";
import { useAuthHook } from "@/utils/useAuthHook";
import { countryList } from "@/utils/countryList";
import SpinnerTemp from "@/components/custom/spinners/SpinnerTemp";
import {
  useGetProjectsQuery,
  useLazyGetProjectsQuery,
} from "@/redux/slices/projects";
import { FormField, FormItem } from "@/components/ui/form";
import { useGetUsersQuery, useLazyGetUsersQuery } from "@/redux/slices/user";
import CustomSelectField from "@/components/CustomSelectField";

import PhoneInput from "react-phone-input-2";
//port "react-phone-input-2/lib/style.css";

interface Prospects {
  id: string;
  name: string;
  phone: string;
  email?: string;
  alternate_phone?: string;
  lead_type?: string;
  marketer?: string;
  lead_source: string;
  project?: string;
  comment?: string;
  category: string;
  city: string;
  country: string;
}

interface AddProspectsProps {
  isOpen: boolean;
  onClose: () => void;
}

export default function AddProspects({ isOpen, onClose }: AddProspectsProps) {
  const { user_details } = useAuthHook();
  const form = useForm(); // Initialize form using react-hook-form

  const [department, setDepartment] = useState<string>("TELEMARKETING");
  useEffect(() => {
    setDepartment(user_details?.department ?? "TELEMARKETING");
  }, []);
  const [marketer, setMarketer] = useState("");
  const [lead_source, setLeadSource] = useState("");
  const [project, setProject] = useState("");

  const [
    fetchLeadSource,
    { data: leadSourcesData, isLoading: loadingLeadSources },
  ] = useLazyGetLeadSourceQuery({});
  const [fetchMarketer, { data: marketersData, isLoading: marketersLoading }] =
    useLazyGetUsersQuery({});
  const [fetchProjects, { data: projects, isLoading: projectsLoading }] =
    useLazyGetProjectsQuery({});
  const [createPropect, { isLoading }] = useAddProspectMutation();

  const [activeTab, setActiveTab] = useState("1");

  const [countryOptions, setCountryOptions] = useState<
    { value: any; label: string }[]
  >([]);
  const [selectedCountry, setSelectedCountry] = useState<{
    label: string;
    value: string;
  } | null>(null);

  const [prospectData, setProspectData] = useState<Prospects>({
    id: "",
    name: "",
    phone: "",
    email: "",
    alternate_phone: "",
    lead_type: "",
    marketer: "",
    lead_source: "",
    project: "",
    comment: "",
    category: "",
    city: "",
    country: "",
  });

  useEffect(() => {
    const countrySelectOptionsList = countryList?.map((country: any) => ({
      value: country?.label,
      label: `${country?.icon} ${country?.label}`,
    }));
    setCountryOptions(countrySelectOptionsList);
  }, [countryList]);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setProspectData((prev) => ({
      ...prev,
      [name]: value,
    }));
  };

  const handleAddProspect = async (e: React.FormEvent) => {
    e.preventDefault();
    console.log("prospectData", prospectData);

    if (
      !prospectData.name ||
      !prospectData.phone ||
      !prospectData?.comment ||
      !prospectData?.category ||
      !lead_source ||
      !selectedCountry?.value ||
      !marketer
    ) {
      toast?.error("Fields marked with * are required");
      return;
    }

    const formData: any = {
      ...prospectData,
      department,
      country: selectedCountry?.value,
      lead_source: lead_source,
      marketer: marketer,
      project: project,
    };

    try {
      const res: any = await createPropect(formData).unwrap();
      if (res?.success) {
        toast.success("Prospect added successfully");
        onClose();
      }
    } catch (error: any) {
      console.error("Error adding prospect:", error);
      if (error?.data) {
        toast.error(error?.data?.message);
      } else {
        toast.error("Failed to add prospect");
      }
      // toast.error("Failed to add prospect");
    }
  };

  return (
    <BaseModal
      size="xl"
      isOpen={isOpen}
      onOpenChange={onClose}
      title="Add Prospect"
      description="Complete all steps to add a new prospect"
      // onStepChange={setCurrentStep}
      // onComplete={handleAddProspect}
    >
      <form onSubmit={handleAddProspect}>
        {activeTab === "1" && (
          <div>
            <div className="space-y-4 py-2">
              <div className="space-y-2">
                <Label htmlFor="Name">
                  Client Full Name <span className="text-red-500">*</span>
                </Label>
                <Input
                  id="Name"
                  name="name"
                  value={prospectData.name}
                  onChange={handleInputChange}
                  placeholder="Enter client name"
                />
              </div>
              <div className="grid md:grid-cols-2 sm:grid-cols-1 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="PhoneNo">
                    Phone Number <span className="text-red-500">*</span>
                  </Label>

                  <PhoneInput
                    value={prospectData.phone}
                    onChange={(value) => {
                      setProspectData((prev) => ({
                        ...prev,
                        phone: `+${value}`,
                      }));
                    }}
                    country={"ke"}
                    enableSearch={true}
                    placeholder="700 111 111"
                  />
                  {/* <Input
                    id="PhoneNo"
                    name="phone"
                    value={prospectData.phone}
                    onChange={handleInputChange}
                    placeholder="Enter phone number (e.g., +2547XXXXXXXX)"
                  /> */}
                </div>
                <div className="space-y-2">
                  <Label htmlFor="alternate_phone">Alternate Phone</Label>
                  <PhoneInput
                    value={prospectData.alternate_phone}
                    onChange={(value) => {
                      setProspectData((prev) => ({
                        ...prev,
                        alternate_phone: `+${value}`,
                      }));
                    }}
                    country={"ke"}
                    enableSearch={true}
                    placeholder="700 111 111"
                  />
                  {/* <Input
                    id="alternate_phone"
                    name="alternate_phone"
                    value={prospectData.alternate_phone}
                    onChange={handleInputChange}
                    placeholder="Enter alternate phone"
                  /> */}
                </div>
              </div>
              <div className="space-y-2">
                <Label htmlFor="email">Client Email</Label>
                <Input
                  id="email"
                  name="email"
                  type="email"
                  value={prospectData.email}
                  onChange={handleInputChange}
                  placeholder="Enter Email"
                />
              </div>

              <div className="space-y-2 c">
                <Label htmlFor="ProjectOfIntrest">Interested Project</Label>
                <div className="space-y-2 flex flex-col my-4">
                  <CustomSelectField
                    valueField="projectId"
                    labelField="name"
                    data={projects}
                    queryFunc={fetchProjects}
                    setValue={setProject}
                    loader={projectsLoading}
                    useSearchField={true}
                  />
                </div>
              </div>
              <div className="grid md:grid-cols-2 sm:grid-cols-1 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="marketer">Allocate Marketer</Label>
                  <div className="space-y-2 flex flex-col my-4">
                    <CustomSelectField
                      valueField="employee_no"
                      labelField="fullnames"
                      data={(marketersData as any)?.data?.results}
                      queryFunc={fetchMarketer}
                      setValue={setMarketer}
                      loader={marketersLoading}
                      useSearchField={true}
                    />
                  </div>
                </div>
                <div className="space-y-2">
                  <FormField
                    control={form.control}
                    name="category"
                    render={({ field }) => (
                      <FormItem>
                        <Label htmlFor="Category">
                          Category <span className="text-red-500">*</span>
                        </Label>
                        <Select
                          onValueChange={(value) => {
                            field.onChange(value); // updates react-hook-form
                            setProspectData((prev) => ({
                              ...prev,
                              category: value,
                            }));
                          }}
                          // defaultValue={field.value}
                        >
                          <SelectTrigger className="w-[100%] border-gray">
                            <SelectValue placeholder="Select a category" />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectGroup>
                              <SelectLabel>Select Category</SelectLabel>
                              <SelectItem value="Hot">Hot</SelectItem>
                              <SelectItem value="Warm">Warm</SelectItem>
                              <SelectItem value="Cold">Cold</SelectItem>
                            </SelectGroup>
                          </SelectContent>
                        </Select>
                      </FormItem>
                    )}
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="lead_source">
                    Lead Source <span className="text-red-500">*</span>
                  </Label>
                  <div className="space-y-2 flex flex-col my-4">
                    <CustomSelectField
                      valueField="id"
                      labelField="name"
                      data={leadSourcesData?.data?.results}
                      queryFunc={fetchLeadSource}
                      setValue={setLeadSource}
                      loader={loadingLeadSources}
                      useSearchField={true}
                    />
                  </div>
                </div>
                <div className="space-y-2">
                  <Label htmlFor="selectedCountry">
                    Country <span className="text-red-500">*</span>
                  </Label>
                  <Multiselect
                    value={selectedCountry}
                    data={countryOptions}
                    setValue={setSelectedCountry}
                    loading={false}
                    isClearable={false}
                    isDisabled={false}
                    isMultiple={false}
                    isSearchable={true}
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="Message">City</Label>
                  <Input
                    id="City"
                    name="city"
                    value={prospectData.city}
                    onChange={handleInputChange}
                    placeholder="Enter City"
                  />
                </div>
              </div>
              <div className="space-y-2">
                <Label htmlFor="Message">Message*</Label>
                <Input
                  id="Message"
                  name="comment"
                  value={prospectData.comment}
                  onChange={handleInputChange}
                  placeholder="Enter message"
                />
              </div>
            </div>
            <div className="w-full flex justify-end my-3 ">
              <ActionButton
                onClick={() => setActiveTab("2")}
                icon={<ChevronRight />}
                iconPosition="right"
                variant="primary"
                className="text-white"
              >
                {" "}
                Next{" "}
              </ActionButton>
            </div>
          </div>
        )}

        {activeTab === "2" && (
          <div className="">
            <div className="space-y-4 py-2">
              <p className="text-sm font-bold">Prospect Summary</p>
              <div className="bg-gray-200 p-4 rounded-md space-y-2">
                <div>
                  <span className="font-medium">Prospect Name:</span>
                  <span className="ml-2">{prospectData.name || "N/A"}</span>
                </div>
                <div>
                  <span className="font-medium">Phone Number:</span>
                  <span className="ml-2">{prospectData.phone || "N/A"}</span>
                </div>
                <div>
                  <span className="font-medium">Alternate phone number:</span>
                  <span className="ml-2">
                    {prospectData.alternate_phone || "N/A"}
                  </span>
                </div>
                <div>
                  <span className="font-medium">Email:</span>
                  <span className="ml-2">{prospectData.email || "N/A"}</span>
                </div>
                <div>
                  <span className="font-medium">Marketer:</span>
                  <span className="ml-2">{marketer || "N/A"}</span>
                </div>
                <div>
                  <span className="font-medium">Lead Source:</span>
                  <span className="ml-2">{lead_source || "N/A"}</span>
                </div>
                <div>
                  <span className="font-medium">Project of Interest:</span>
                  <span className="ml-2">{project || "N/A"}</span>
                </div>
                <div>
                  <span className="font-medium">Category:</span>
                  <span className="ml-2">{prospectData.category || "N/A"}</span>
                </div>
                <div>
                  <span className="font-medium">Country:</span>
                  <span className="ml-2">
                    {selectedCountry?.label || "N/A"}
                  </span>
                </div>
                <div>
                  <span className="font-medium">City:</span>
                  <span className="ml-2">{prospectData.city || "N/A"}</span>
                </div>
                <div>
                  <span className="font-medium">Message:</span>
                  <span className="ml-2">{prospectData.comment || "N/A"}</span>
                </div>
              </div>
              <p className="text-sm text-gray-600">
                Please review the information above before adding this prospect.
              </p>
            </div>
            <div className="flex items-center justify-between flex-wrap">
              <ActionButton
                onClick={() => setActiveTab("1")}
                icon={<ChevronLeft />}
                iconPosition="left"
                variant="secondary"
              >
                {" "}
                Previous{" "}
              </ActionButton>
              {isLoading ? (
                <SpinnerTemp size="sm" />
              ) : (
                <ActionButton
                  type="submit"
                  icon={<Send />}
                  iconPosition="right"
                  variant="primary"
                  className="text-white"
                >
                  {" "}
                  Submit{" "}
                </ActionButton>
              )}
            </div>
          </div>
        )}
      </form>
    </BaseModal>
  );
}
