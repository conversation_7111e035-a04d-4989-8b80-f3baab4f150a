import { useState } from "react";
import BaseModal from "@/components/custom/modals/BaseModal";
import { Settings, AlertCircle, Loader2, User, Target, TrendingUp, DollarSign, Calendar, MapPin, MoreVertical } from "lucide-react";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { useGetHQKarenQuery } from "@/redux/slices/hrDashboardApiSlice";
import { Badge } from "@/components/custom/badges/badges";
import { cn } from "@/lib/utils";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { MarketerReport } from "@/types/marketer";

interface OfficeTableModalProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  office?: string;
  start_date?: string;
  end_date?: string;
  period_name?: string;
}

export default function OfficeTableModal({
  open,
  onOpenChange,
  office,  // This is the office prop
  start_date,
  end_date,
  period_name
}: OfficeTableModalProps) {
  // Remove the office filter state since we want to use the prop
  // const [officeFilter, setOfficeFilter] = useState("ALL");
  const [periodFilter, setPeriodFilter] = useState("ALL");
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(50);

  // Use the office prop directly in the query
  const {
    data: reportsData,
    isLoading,
    error
  } = useGetHQKarenQuery({
    office: office, // Use the prop directly instead of officeFilter
    period: periodFilter,
    page: currentPage,
    page_size: pageSize
  });

  // Skip the query if no office is provided
  if (!office) {
    return (
      <div className="flex items-center justify-center py-12">
        <div className="text-center">
          <AlertCircle className="h-8 w-8 text-orange-600 mx-auto mb-4" />
          <p className="text-orange-600">Please select an office</p>
        </div>
      </div>
    );
  }

  // Extract the results array from the API response and filter by date range
  const allData: MarketerReport[] = reportsData?.results || [];

  // Filter data to show ALL marketers within the specified date range
  const data: MarketerReport[] = allData.filter(record => {
    if (!start_date || !end_date) return true; // If no date range specified, show all

    const recordStartDate = new Date(record.period_start_date);
    const recordEndDate = new Date(record.period_end_date);
    const filterStartDate = new Date(start_date);
    const filterEndDate = new Date(end_date);

    // Check if the record's period overlaps with the filter period
    return recordStartDate <= filterEndDate && recordEndDate >= filterStartDate;
  });

  // Helper function to format currency
  const formatCurrency = (value: number | null | undefined): string => {
    try {
      if (value === null || value === undefined || isNaN(value)) return "KES 0";
      return new Intl.NumberFormat('en-KE', {
        style: 'currency',
        currency: 'KES',
        minimumFractionDigits: 0,
      }).format(value);
    } catch (error) {
      console.error("Error formatting currency:", error);
      return "KES 0";
    }
  };

  // Helper function to format percentage
  const formatPercentage = (value: number | null | undefined): number => {
    if (value === null || value === undefined || isNaN(value)) return 0;
    return value;
  };

  // Helper function to get performance color
  const getPerformanceColor = (percentage: number) => {
    if (percentage >= 100) return "bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400";
    if (percentage >= 75) return "bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-400";
    if (percentage >= 50) return "bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-400";
    return "bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-400";
  };

  // Helper function to get status color
  const getStatusColor = (status: string) => {
    return status === "Open" 
      ? "bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400"
      : "bg-gray-100 text-gray-800 dark:bg-gray-900/30 dark:text-gray-400";
  };

  // Helper function to determine office type based on title
  const getOfficeType = (title: string) => {
    if (title === "HOS") return "HQ";
    if (title === "GM") return "Global";
    return title; // fallback to original title
  };

  // Render individual marketer card - Optimized for modal
  const renderMarketerCard = (marketer: MarketerReport, index: number) => (
    <Card key={`${marketer.marketer_no || index}`} className="hover:shadow-md transition-shadow duration-200 border border-gray-200 dark:border-gray-700 h-fit">
      <CardHeader className="pb-2">
        <div className="flex items-start justify-between">
          <div className="flex-1 min-w-0">
            <CardTitle className="text-base font-semibold text-gray-900 dark:text-white flex items-center gap-2 mb-1">
              <User className="w-4 h-4 text-blue-600 flex-shrink-0" />
              <span className="truncate">{marketer.marketer_name || "Unknown Marketer"}</span>
            </CardTitle>
            <div className="flex items-center gap-2 text-xs text-gray-600 dark:text-gray-400 flex-wrap">
              <MapPin className="w-3 h-3 flex-shrink-0" />
              <span>{getOfficeType(marketer.title ?? "")}</span>
              <span className="text-gray-400">•</span>
              <span>No: {marketer.marketer_no || "N/A"}</span>
            </div>
          </div>
          <div className="flex items-center gap-2 flex-shrink-0 ml-2">
            <Badge
              className={cn(
                "text-xs font-medium",
                getPerformanceColor(formatPercentage(marketer.MIB_Perfomance))
              )}
            >
              {formatPercentage(marketer.MIB_Perfomance).toFixed(1)}%
            </Badge>
            <DropdownMenu>
              <DropdownMenuTrigger className="p-1 hover:bg-gray-100 dark:hover:bg-gray-700 rounded">
                <MoreVertical className="w-4 h-4" />
              </DropdownMenuTrigger>
              <DropdownMenuContent>
                <DropdownMenuLabel>Actions</DropdownMenuLabel>
                <DropdownMenuSeparator />
                <DropdownMenuItem
                  onClick={(e) => {
                    e.stopPropagation();
                    console.log(`View details for ${marketer.marketer_no}`);
                  }}
                >
                  View Details
                </DropdownMenuItem>
                <DropdownMenuItem
                  onClick={(e) => {
                    e.stopPropagation();
                    console.log(`Edit marketer ${marketer.marketer_no}`);
                  }}
                >
                  Edit
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
        </div>
        <div className="flex items-center gap-2 mt-1">
          <Badge
            className={cn(
              "text-xs",
              getStatusColor(marketer.status ?? "")
            )}
          >
            {marketer.status}
          </Badge>
          <span className="text-xs text-gray-500">{marketer.title}</span>
        </div>
      </CardHeader>
      
      <CardContent className="space-y-3 pt-0">
        {/* Period Information - Compact */}
        <div className="grid grid-cols-2 gap-2 p-2 bg-gray-50 dark:bg-gray-800 rounded text-xs">
          <div className="flex items-center gap-1">
            <Calendar className="w-3 h-3 text-green-600 flex-shrink-0" />
            <div className="min-w-0">
              <p className="text-xs text-gray-500 dark:text-gray-400">Start</p>
              <p className="font-medium truncate">
                {marketer.period_start_date ? new Date(marketer.period_start_date).toLocaleDateString('en-KE', { month: 'short', day: 'numeric' }) : "N/A"}
              </p>
            </div>
          </div>
          <div className="flex items-center gap-1">
            <Calendar className="w-3 h-3 text-green-600 flex-shrink-0" />
            <div className="min-w-0">
              <p className="text-xs text-gray-500 dark:text-gray-400">End</p>
              <p className="font-medium truncate">
                {marketer.period_end_date ? new Date(marketer.period_end_date).toLocaleDateString('en-KE', { month: 'short', day: 'numeric' }) : "N/A"}
              </p>
            </div>
          </div>
        </div>

        {/* Key Metrics - Grid layout */}
        <div className="grid grid-cols-2 gap-3 text-xs">
          <div className="space-y-2">
            <div className="flex items-center gap-1">
              <Target className="w-3 h-3 text-blue-600 flex-shrink-0" />
              <div className="min-w-0">
                <p className="text-gray-500 dark:text-gray-400">Monthly</p>
                <p className="font-semibold text-blue-600 truncate">
                  {formatCurrency(marketer.monthly_target).replace('KES ', 'K')}
                </p>
              </div>
            </div>
            
            <div className="flex items-center gap-1">
              <TrendingUp className="w-3 h-3 text-green-600 flex-shrink-0" />
              <div className="min-w-0">
                <p className="text-gray-500 dark:text-gray-400">Achieved</p>
                <p className="font-semibold text-green-600 truncate">
                  {formatCurrency(marketer.MIB_achieved).replace('KES ', 'K')}
                </p>
              </div>
            </div>
          </div>

          <div className="space-y-2">
            <div className="flex items-center gap-1">
              <Target className="w-3 h-3 text-purple-600 flex-shrink-0" />
              <div className="min-w-0">
                <p className="text-gray-500 dark:text-gray-400">Daily</p>
                <p className="font-semibold text-purple-600 truncate">
                  {formatCurrency(marketer.daily_target).replace('KES ', 'K')}
                </p>
              </div>
            </div>
            
            <div className="flex items-center gap-1">
              <DollarSign className="w-3 h-3 text-orange-600 flex-shrink-0" />
              <div className="min-w-0">
                <p className="text-gray-500 dark:text-gray-400">Commission</p>
                <p className="font-semibold text-orange-600 truncate">
                  {formatCurrency(marketer.commission_payable).replace('KES ', 'K')}
                </p>
              </div>
            </div>
          </div>
        </div>

        {/* Performance Progress Bar - Compact */}
        <div className="space-y-1">
          <div className="flex justify-between items-center">
            <span className="text-xs text-gray-500 dark:text-gray-400">Performance</span>
            <span className="text-xs font-medium">
              {formatPercentage(marketer.MIB_Perfomance).toFixed(1)}%
            </span>
          </div>
          <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-1.5">
            <div
              className={cn(
                "h-1.5 rounded-full transition-all duration-300",
                formatPercentage(marketer.MIB_Perfomance) >= 100 ? "bg-green-500" :
                formatPercentage(marketer.MIB_Perfomance) >= 75 ? "bg-blue-500" :
                formatPercentage(marketer.MIB_Perfomance) >= 50 ? "bg-yellow-500" : "bg-red-500"
              )}
              style={{ width: `${Math.min(formatPercentage(marketer.MIB_Perfomance), 100)}%` }}
            />
          </div>
        </div>

        {/* Commission Rate - Compact */}
        <div className="flex items-center justify-center p-2 bg-blue-50 dark:bg-blue-900/20 rounded text-xs">
          <span className="text-gray-600 dark:text-gray-400">Rate: </span>
          <span className="font-semibold text-blue-600 ml-1">
            {marketer.commission_rate ? `${(marketer.commission_rate * 100).toFixed(1)}%` : "N/A"}
          </span>
        </div>
      </CardContent>
    </Card>
  );

  return (
    <BaseModal
      open={open}
      onOpenChange={onOpenChange}
      title={`Marketer Performance - ${period_name || 'Office Reports'}`}
      description={`View marketer performance for ${office || 'selected office'} ${period_name ? `during ${period_name}` : ''}`}
      className="max-w-[95vw] w-full"
      size="lg"
    >
      <div className="p-4 bg-white dark:bg-gray-900 rounded-md max-w-full overflow-hidden">
        {isLoading ? (
          <div className="flex items-center justify-center py-12">
            <div className="text-center">
              <Loader2 className="h-8 w-8 animate-spin text-green-600 mx-auto mb-4" />
              <p className="text-gray-600 dark:text-gray-400">Loading marketer data...</p>
            </div>
          </div>
        ) : error ? (
          <div className="flex items-center justify-center py-12">
            <div className="text-center">
              <AlertCircle className="h-8 w-8 text-red-600 mx-auto mb-4" />
              <p className="text-red-600 dark:text-red-400">Failed to load marketer data</p>
              <p className="text-sm text-gray-500 dark:text-gray-400 mt-2">Please try again later</p>
            </div>
          </div>
        ) : !office || !start_date || !end_date ? (
          <div className="flex items-center justify-center py-12">
            <div className="text-center">
              <AlertCircle className="h-8 w-8 text-orange-600 mx-auto mb-4" />
              <p className="text-orange-600 dark:text-orange-400">Missing required parameters</p>
              <p className="text-sm text-gray-500 dark:text-gray-400 mt-2">Office and date range are required</p>
            </div>
          </div>
        ) : (
          <div className="w-full h-[calc(80vh-200px)] overflow-y-auto">
            {/* Summary Section - Made more compact */}
            <div className="mb-4 p-3 bg-gradient-to-r from-blue-50 to-green-50 dark:from-blue-900/20 dark:to-green-900/20 rounded-lg border border-gray-200 dark:border-gray-700">
              <div className="flex items-center justify-between gap-3">
                <div>
                  <h3 className="text-md font-semibold text-gray-900 dark:text-white">
                    {office} Office Performance
                  </h3>
                  <p className="text-xs text-gray-600 dark:text-gray-400">
                    {period_name || 'Selected Period'}
                  </p>
                </div>
                <div className="flex items-center gap-4">
                  <div className="text-center">
                    <div className="text-lg font-bold text-green-600 dark:text-green-400">
                      {reportsData?.count.toLocaleString() || 0}
                    </div>
                    <div className="text-xs text-gray-500 dark:text-gray-400">Total</div>
                  </div>
                  <div className="text-center">
                    <div className="text-lg font-bold text-blue-600 dark:text-blue-400">
                      {data.length}
                    </div>
                    <div className="text-xs text-gray-500 dark:text-gray-400">Showing</div>
                  </div>
                </div>
              </div>
            </div>

            {/* Cards Grid - Optimized for modal */}
            {data.length > 0 ? (
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
                {data.map((marketer, index) => renderMarketerCard(marketer, index))}
              </div>
            ) : (
              <div className="flex items-center justify-center py-12">
                <div className="text-center">
                  <AlertCircle className="h-8 w-8 text-gray-400 mx-auto mb-4" />
                  <p className="text-gray-600 dark:text-gray-400">No marketer data available</p>
                  <p className="text-sm text-gray-500 dark:text-gray-500 mt-2">
                    No records found for {office} office in the selected period
                  </p>
                </div>
              </div>
            )}
          </div>
        )}
      </div>
    </BaseModal>
  );
}