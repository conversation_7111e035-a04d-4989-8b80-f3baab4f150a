# Credits Team Dashboard

A comprehensive dashboard for managing and tracking credit officers' performance, similar to the Legal Team's Offer Letters Dashboard.

## Features

### 📊 Dashboard Overview
- **Collections Performance**: Display key metrics including overdue collections, collections collected, installments due today, and deposits below threshold
- **Period Information**: Shows current period dates for tracking
- **Credit Officers Grid**: Card-based layout displaying all credit officers with search functionality

### 🔍 Search & Filter
- **Real-time Search**: Search credit officers by name or officer ID
- **Responsive Design**: Optimized for desktop, tablet, and mobile devices
- **Performance Indicators**: Visual badges showing officer performance levels

### 📋 Detailed Officer View
- **Modal Interface**: Click any credit officer card to view detailed information
- **Comprehensive Metrics**: 
  - Daily performance (installments due/collected today)
  - Monthly performance (installments due/collected)
  - Portfolio summary (customers, sales, total value)
  - Collections performance
  - Additional deposits and final payments

### 🎨 UI/UX Features
- **Professional Design**: Clean, modern interface with consistent styling
- **Smooth Animations**: Fade-in animations for cards and loading states
- **Dark Mode Support**: Full dark/light theme compatibility
- **Loading States**: Proper loading indicators and error handling
- **Responsive Layout**: Adapts to different screen sizes

## API Integration

### Endpoints Used
1. **`/accounts-credits-team-index/`** - Fetches overview data and list of credit officers
2. **`/accounts-credits-team-details/`** - Fetches detailed information for a specific credit officer

### Data Structure
```typescript
// Overview Response
interface CreditsTeamIndexResponse {
  Collections: {
    Installments_Due_Today: number;
    Overdue_Collections_Collected: number;
    Overdue_Collections: number;
    ALL_Overdue_Collections: number;
    Sales_Deposits_Below_Threshold: number;
    Overdue_Below_Threshold: number;
    Expected_Monthly_Installments: number;
    EXPECTED_Monthly_installments_collected: number;
  };
  Current_Month: {
    Period_Start_Date: string;
    Period_End_Date: string;
  };
  Credits_Teams_Performance: CreditOfficer[];
}

// Officer Details Response
interface CreditOfficerDetailsResponse {
  installments_due_today: number;
  installments_due_today_total: number;
  overdue_collections_collected: number;
  overdue_collections: number;
  all_overdue_collections: number;
  Current_Month: {
    Period_Start_Date: string;
    Period_End_Date: string;
  };
  sales_below_threshold_count: number;
  overdue_below_threshold_count: number;
  monthly_installments_due: number;
  monthly_installments_due_collected: number;
  total_expexted_installments: number;
  Portfolio: {
    all_customers: number;
    all_sales: number;
    portfolio_total_paid: number;
  };
  installments_collected_today: number;
  additionaldeposits_installments_collected: number;
  finalpaymentscollected_mib: number;
  finalpaymentscollected_no_of_payments: number;
}
```

## File Structure

```
src/pages/CustomDshboard/CreditsTeam/
├── CreditsTeamDashboard.tsx      # Main dashboard component
├── CreditOfficerDetailModal.tsx  # Detailed view modal
├── animations.css                # CSS animations and styles
└── README.md                     # This documentation
```

## Components

### CreditsTeamDashboard
- Main dashboard component
- Handles search functionality
- Manages modal state
- Displays overview cards and credit officers grid

### CreditOfficerDetailModal
- Modal component for detailed officer information
- Fetches and displays comprehensive officer data
- Performance indicators and portfolio summary
- Responsive design with proper error handling

## Routing

The dashboard is accessible via:
- **Route**: `/credits-team/dashboard`
- **Navigation**: Teams → Credits Team → Credits Dashboard
- **Permissions**: Requires permission `113` (Teams access)

## Styling

- Uses Tailwind CSS for styling
- Custom animations defined in `animations.css`
- Consistent with existing dashboard designs
- Professional color scheme with proper contrast
- Responsive grid layouts

## Performance Features

- **Lazy Loading**: Modal content loads only when opened
- **Caching**: Redux RTK Query handles API response caching
- **Optimized Rendering**: Memoized search filtering
- **Error Boundaries**: Proper error handling and retry mechanisms

## Usage

1. Navigate to Teams → Credits Team → Credits Dashboard
2. View overview metrics at the top of the page
3. Use the search bar to find specific credit officers
4. Click on any credit officer card to view detailed information
5. Use the refresh button to reload data

## Future Enhancements

- Export functionality for reports
- Advanced filtering options
- Performance comparison charts
- Bulk actions for multiple officers
- Real-time data updates
- Performance trends and analytics
