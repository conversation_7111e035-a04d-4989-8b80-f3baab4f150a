import React, { useState } from "react";
import {
  Sidebar,
  <PERSON><PERSON><PERSON>ontent,
  <PERSON>bar<PERSON>ooter,
  SidebarHeader,
} from "@/components/ui/sidebar";
import { TeamSwitcher } from "@/app-components/sidebar/team-switcher";
import { NavMain } from "@/app-components/sidebar/nav-main";
import { useTheme } from "@/hooks/use-theme";
import {
  useSidebarPermissions,
  SidebarPermissionKey,
} from "../../hooks/useSidebarPermissions";

import {
  Home,
  ShoppingBag,
  UserPlus,
  Users,
  Truck,
  Box,
  CreditCard,
  FileText,
  Repeat,
  Map,
  Key,
  UserCheck,
  GitGraph,
  BarChart2,
  Target,
  DollarSign,
  PieChart,
  Globe,
  PhoneCall,
  Cpu,
  TrendingUp,
  CalendarCheck,
  CheckSquare,
  AlertCircle,
  MessageCircle,
  Ticket,
  Flag,
  Bell,
  FileEdit,
  List,
  Clock,
  Search,
  Grid,
  User,
  Shield,
  Briefcase,
  Building,
  MapPin,
  GrapeIcon,
  Bus,
  Sun,
  Moon,
  WalletCards,
  CircuitBoard,
  BarChart,
  ChartNoAxesCombined,
  <PERSON><PERSON>ie,
  ChartColumnBig,
  ChartBarDecreasing,
  ChartBarStacked,
  ChartArea,
  Group,
  Users2,
  KeyRound,
  DollarSignIcon,
  Monitor,
} from "lucide-react";
import { NavUser } from "./nav-user";
import useNavStore from "@/zustand/useNavStore";

const navData = {
  teams: [{ name: "Optiven Limited", logo: Cpu, plan: "Enterprise" }],
  navMain: [
    { title: "Dashboard", url: "/", icon: Home, isActive: false },
    {
      title: "Sales",
      url: "",
      icon: ShoppingBag,
      items: [
        { title: "All Sales", url: "/sales/overview#all-sales" },
        { title: "On Going Sales", url: "/sales/overview#on-going-sales" },
        { title: "Completed Sales", url: "/sales/overview#completed-sales" },
        { title: "Dropped Sales", url: "/sales/overview#dropped-sales" },
        { title: "Report Demo", url: "/sales/report" },
      ],
    },
    { title: "Prospects", url: "/prospects", icon: GitGraph },
    {
      title: "Customers",
      url: "",
      icon: Users,
      items: [
        { title: "All Customers", url: "/customers/overview#all-customers" },
        // {
        //   title: "Active Customers",
        //   url: "/customers/overview#active-customers",
        // },
        // {
        //   title: "Completed Customers",
        //   url: "/customers/overview#completed-customers",
        // },
        // {
        //   title: "Dropped Customers",
        //   url: "/customers/overview#dropped-customers",
        // },
        // { title: "Report Demo", url: "/sales/report" },
      ],
    },

    {
      title: "Logistics",
      url: "/logistics",
      icon: Truck,
      items: [
        { title: "Dashboard", url: "/logistics-dash" },
        { title: "Logistics Stats", url: "/logistics-stats", icon: BarChart2 },
        { title: "Clients", url: "/clients", icon: Users },
        { title: "Drivers", url: "/drivers", icon: UserCheck },
        {
          title: "Vehicles",
          url: "/logistics/vehicles",
          icon: FileText,
          items: [
            {
              title: "Vehicle Details",
              url: "/logistics/vehicles#vehicle-details",
            },
            {
              title: "Request a Vehicle",
              url: "/logistics/vehicles#vehicle-requests",
            },
          ],
        },
        {
          title: "Logistics Reports",
          url: "/logistics-reports",
          icon: FileText,
        },
        {
          title: " Site Visit Reports",
          url: "/allsitevisitreport",
          icon: GrapeIcon,
        },
      ],
    },
    {
      title: "Inventory",
      url: "/inventory",
      icon: Box,
      items: [
        { title: "Dashboard", url: "/projects", icon: Home },
        {
          title: "My Booking",
          url: "",
          icon: FileText,
          items: [
            {
              title: "My Bookings",
              url: "/mybookings#ALL",
              icon: Globe,
            },
            {
              title: "Reservations",
              url: "/diaspora-reservations",
              icon: CheckSquare,
            },
            {
              title: "Diaspora Bookings",
              url: "/mybookings#DIASPORA",
              icon: AlertCircle,
            },
            {
              title: "Special Booking",
              url: "/mybookings#SPECIAL",
              icon: CheckSquare,
            },
            {
              title: "Mpesa Booking",
              url: "/mybookings#MPESA",
              icon: DollarSign,
            },
            {
              title: "Other Booking",
              url: "/mybookings#OTHER",
              icon: Repeat,
            },
          ],
        },
        {
          title: "Special Bookings",
          url: "/inventory/booking-approvals",
          icon: Clock,
        },
        {
          title: "Accounts",
          url: "",
          icon: CreditCard,
          items: [
            {
              title: "Active Bookings",
              url: "/inventory/accounts/all-bookings",
              icon: List,
            },
            {
              title: "Archived Bookings",
              url: "/inventory/accounts/archived-bookings",
              icon: List,
            },
            {
              title: "Reports",
              url: "/inventory/accounts/reports",
              icon: List,
            },
          ],
        },
        {
          title: "Diaspora",
          url: "",
          icon: Globe,
          items: [
            { title: "Trips", url: "/diaspora-trips", icon: Map },
            { title: "All Trips", url: "/diaspora-trips/all", icon: Map },
            {
              title: "Diaspora Reservations",
              url: "/diaspora-reservations",
              icon: Map,
            },
            {
              title: "Diaspora Receipts",
              url: "/diaspora-receipts",
              icon: FileText,
            },
          ],
        },
        {
          title: "Reports",
          url: "",
          icon: FileText,
          items: [
            {
              title: "Marketers Reports",
              url: "/plotmarketreport",
              icon: BarChart2,
            },
            { title: "Project Reports", url: "", icon: FileText },
          ],
        },
        {
          title: "Pricing",
          url: "",
          icon: DollarSign,
          items: [
            {
              title: "Project Pricing",
              url: "/inventory/pricing/project-pricing",
              icon: DollarSign,
            },
            {
              title: "Payment Plan Checker",
              url: "/inventory/pricing/payment-plan-checker",
              icon: CalendarCheck,
            },
          ],
        },
        {
          title: "Mpesa Transactions",
          url: "/mpesa-transactions",
          icon: CreditCard,
          items: [],
        },
        {
          title: "Inventory Logs",
          url: "/inventory/logs",
          icon: FileEdit,
          items: [],
        },
      ],
    },
    // {
    //   title: "Cashback",
    //   url: "/logistics/cashback",
    //   icon: CreditCard,
    //   items: [],
    // },
    // {
    //   title: "Offer Letters",
    //   url: "/offer-letters",
    //   icon: FileText,
    //   items: [],
    // },
    // { title: "Offer Letter", url: "/ol", icon: Cpu },
  ],

  NavPerformance: [
    // Marketer sections (7113) - My Targets, My Commissions, My Cash on Cash, My Portfolio
    { title: "Marketer Targets", url: "/marketer-targets", icon: Target },
    {
      title: "Marketer Commissions",
      url: "/marketer-commissions",
      icon: DollarSign,
    },
    { title: "Cash On Cash", url: "/cash-on-cash", icon: PieChart },
    { title: "Portfolio", url: "/portfolio", icon: Briefcase },

    // GM sections (7114, 7115) - Marketer Performance, Marketer Portfolio
    { title: "Marketer Performance", url: "/performance", icon: BarChart2 },

    
  ],

  navAdmin: [
    {
      title: "Users",
      url: "/admin/users",
      icon: Users,
      items: [
        { title: "Users List", url: "/users-list", icon: List },
        { title: "Users Grid", url: "/users-grid", icon: Grid },
        { title: "View Profile", url: "/profile", icon: User },
      ],
    },
    {
      title: "Role & Access",
      url: "/admin/roles",
      icon: Key,
      items: [
        { title: "Permissions", url: "/permissions", icon: Shield },
        { title: "Groups", url: "/groups", icon: Users },
      ],
    },
    { title: "Users List", url: "/users-list", icon: List },
    { title: "Teams", url: "/admin/teams", icon: Users2 },
    { title: "Groups", url: "/admin/groups", icon: Group },
    { title: "Permissions", url: "/permissions", icon: KeyRound },
    { title: "Assign Role", url: "/admin/assign-role", icon: UserCheck },
    { title: "Maps", url: "/admin/maps", icon: Map },
  ],

  NavTeams: [
    // Team Leader sections (7123) - All marketer sections + Marketer Performance
    // { title: "Office Performance", url: "/office-performance", icon: Cpu },
    { title: "Targets", url: "/targets", icon: Target },
    { title: "Commissions", url: "/commissions", icon: DollarSign },
    { title: "Team Performance", url: "/team-performance", icon: Users },
    // { title: "Profiles", url: "/profiles", icon: User },
  ],

  NavDepartments: [
    { title: "Directors", url: "/directors", icon: Home },
    { title: "Accounts", url: "/accounts-dashboard", icon: ChartColumnBig },
     {
      title: "GM",
      url: "/",
      icon: Briefcase,
      items: [
        { title: "Karen", url: "/gm-karen-dashboard", icon: ChartPie },
    { title: "HQ ", url: "/hq-hos-dashboard", icon: ChartNoAxesCombined },
      ],
    },
    { title: "Diaspora", url: "/diaspora-dashboard", icon: ChartBarDecreasing },

    {
      title: "Credits Team",
      url: "/credits-team-dashboard",
      icon: ChartBarStacked,
    },
    // {
    //   title: "Marketer Dashboard",
    //   url: "/marketer-dashboard",
    //   icon: ChartArea,
    // },

    {
      title: "Credits Team",
      url: "/credits-team",
      icon: DollarSign,
      items: [
        {
          title: "Credits Dashboard",
          url: "/credits-team/dashboard",
        },
      ],
    },
    // { title: "HQ Team", url: "hq-team", icon: Building },
    // { title: "Karen Team", url: "karen-team", icon: MapPin },
    // { title: "Diaspora", url: "diaspora", icon: Globe },

    { title: "Hr team", url: "/hr-team", icon: Building },
    {
      title: "Legal Team",
      url: "/legal-team",
      icon: Briefcase,
      items: [
        // { title: "Legal Dashboard", url: "/legal-team" },
        {
          title: "Offer Letters Dashboard",
          url: "/legal-team/offer-letters-dashboard",
        },
      ],
    },
    { title: "DataTeam", url: "/data-team", icon: BarChart },

    {
      title: "TeleMarketing Team",
      url: "/tele-marketing-team",
      icon: PhoneCall,
    },
    { title: "Digital Team", url: "/digital-team", icon: Cpu },
  ],

  NavReports: [
    { title: "Sales Reports", url: "/reports/sales-reports", icon: ChartPie },
    { title: "Prospect Reports", url: "/money-in-reports", icon: DollarSign },
    {
      title: "Customer Reports",
      url: "/installment-reports",
      icon: CalendarCheck,
    },
    {
      title: "Customer Reports",
      url: "/customer-reports",
      icon: CalendarCheck,
    },
    { title: "Inventory Reports", url: "/project-summary", icon: FileText },
    { title: "Logistics Reports", url: "/logistics-reports", icon: Bus },
  ],

  NavAnalytics: [
    { title: "Sales Analytics", url: "/new-sales", icon: TrendingUp },
    {
      title: "Prospect Analytics",
      url: "/money-in-Analytics",
      icon: DollarSign,
    },
    {
      title: "Customer Analytics",
      url: "/installment-Analytics",
      icon: CalendarCheck,
    },
    { title: "Inventory Analytics", url: "/project-summary", icon: FileText },
    { title: "Logistics Analytics", url: "/logistics-reports", icon: Bus },
  ],

  NavServices: [
    { title: "To-Do", url: "/admin/services", icon: CheckSquare },
    // { title: "Complaints", url: "/complaints", icon: AlertCircle },
    // { title: "Feedback", url: "/feedback", icon: MessageCircle },
    {
      title: "Ticketing",
      url: "/ticketing",
      icon: Ticket,
      items: [
        { title: "Tickets", url: "/ticketing" },
        { title: "Tickets Sources", url: "/ticketing/sources" },
        { title: "Tickets Categories", url: "/ticketing/categories" },
        { title: "Tickets Logs", url: "/ticketing/logs" },
      ],
    },
    { title: "Flags", url: "/flags", icon: Flag },
    { title: "Engagements", url: "/engagements", icon: Users },
    { title: "Notifications", url: "/notifications", icon: Bell },
    { title: "Notes", url: "/notes", icon: FileText },
    { title: "Reminders", url: "/reminders", icon: Clock },
    // { title: "Forms", url: "/forms", icon: FileEdit },
    // { title: "Surveys", url: "/surveys", icon: List },
  ],
};

export function AppSidebar(props: React.ComponentProps<typeof Sidebar>) {
  const { levelOne, setLevelOne } = useNavStore() as {
    levelOne: string | null;
    setLevelOne: (value: string | null) => void;
  };
  const [openSection, setOpenSection] = useState<string | null>("Main");
  const {
    hasSidebarAccess,
    hasLogisticsPermission,
    hasInventoryPermission,
    hasPerformancePermission,
    isLoading: loadingPermissions,
  } = useSidebarPermissions();
  // const { theme, toggleTheme } = useTheme();

  // Filter logistics navigation items based on permissions
  const filterLogisticsItems = (items: any[]) => {
    return items.filter((item) => {
      // Check specific logistics permissions
      switch (item.url) {
        case "/logistics-dash":
          return hasLogisticsPermission("ACCESS_LOGISTICS_DASHBOARD");
        case "/logistics-stats":
          return hasLogisticsPermission("ACCESS_LOGISTICS_STATISTICS");
        case "/clients":
          return hasLogisticsPermission("ACCESS_CLIENTS");
        case "/drivers":
          return hasLogisticsPermission("ACCESS_DRIVERS");
        case "/logistics/vehicles":
          return hasLogisticsPermission("ACCESS_VEHICLES");
        case "/logistics-reports":
          return hasLogisticsPermission("ACCESS_LOGISTICS_REPORTS");
        case "/allsitevisitreport":
          return hasLogisticsPermission("ACCESS_SITEVISIT_REPORTS");
        default:
          return true; // Allow other items by default
      }
    });
  };

  // Check if user has any inventory permissions
  const hasAnyInventoryPermission = () => {
    return (
      hasInventoryPermission("VIEW_INVENTORY_FULL_ACCESS") ||
      hasInventoryPermission("VIEW_INVENTORY_MARKETER") ||
      hasInventoryPermission("VIEW_INVENTORY_GM_HQ") ||
      hasInventoryPermission("VIEW_INVENTORY_GM_KAREN") ||
      hasInventoryPermission("VIEW_INVENTORY_ACCOUNTS") ||
      hasInventoryPermission("VIEW_INVENTORY_DIASPORA") || // Still needed for My Booking access
      hasInventoryPermission("VIEW_INVENTORY_REPORTS") ||
      hasInventoryPermission("VIEW_INVENTORY_PRICING") ||
      hasInventoryPermission("VIEW_INVENTORY_MPESA_TRANSACTIONS") ||
      hasInventoryPermission("VIEW_INVENTORY_LOGS") ||
      hasInventoryPermission("VIEW_INVENTORY_MAPS") ||
      hasInventoryPermission("VIEW_INVENTORY_DIASPORA_REGIONAL_MANAGER") ||
      hasInventoryPermission("VIEW_INVENTORY_DIASPORA_MANAGER_KASAYA")
    );
  };

  // Filter inventory navigation items based on permissions
  const filterInventoryItems = (items: any[]) => {
    return items.filter((item) => {
      // If user has full access, show all inventory items
      if (hasInventoryPermission("VIEW_INVENTORY_FULL_ACCESS")) {
        return true;
      }

      // Check specific inventory permissions
      switch (item.title) {
        case "My Booking":
          const hasMyBookingAccess =
            hasInventoryPermission("VIEW_INVENTORY_MARKETER") ||
            hasInventoryPermission("VIEW_INVENTORY_DIASPORA");
          if (!hasMyBookingAccess) return false;

          // Filter My Booking subitems based on specific permissions
          if (item.items) {
            item.items = item.items.filter((subItem: any) => {
              // For marketers with 7117 permission - show My Bookings, Reservations, Diaspora Bookings
              if (hasInventoryPermission("VIEW_INVENTORY_DIASPORA")) {
                return [
                  "My Bookings",
                  "Reservations",
                  "Diaspora Bookings",
                ].includes(subItem.title);
              }
              // For other marketer permissions - show additional booking types
              if (hasInventoryPermission("VIEW_INVENTORY_MARKETER")) {
                return [
                  "My Bookings",
                  "Special Booking",
                  "Mpesa Booking",
                  "Other Booking",
                ].includes(subItem.title);
              }
              return false;
            });
          }
          return true;

        case "Special Bookings":
          return (
            hasInventoryPermission("VIEW_INVENTORY_GM_HQ") ||
            hasInventoryPermission("VIEW_INVENTORY_GM_KAREN")
          );

        case "Accounts":
          return hasInventoryPermission("VIEW_INVENTORY_ACCOUNTS");

        case "Diaspora":
          const hasDiasporaAccess =
            hasInventoryPermission("VIEW_INVENTORY_DIASPORA") ||
            hasInventoryPermission(
              "VIEW_INVENTORY_DIASPORA_REGIONAL_MANAGER"
            ) ||
            hasInventoryPermission("VIEW_INVENTORY_DIASPORA_MANAGER_KASAYA");
          if (!hasDiasporaAccess) return false;

          // Filter Diaspora subitems based on specific permissions
          if (item.items) {
            item.items = item.items.filter((subItem: any) => {
              // For Diaspora Regional Manager (7123) - show only "Trips"
              if (
                hasInventoryPermission(
                  "VIEW_INVENTORY_DIASPORA_REGIONAL_MANAGER"
                )
              ) {
                return subItem.title === "Trips";
              }
              // For Diaspora Manager Kasaya (7124) - show only "All Trips"
              if (
                hasInventoryPermission("VIEW_INVENTORY_DIASPORA_MANAGER_KASAYA")
              ) {
                return subItem.title === "All Trips";
              }
              // For regular diaspora permission (7117) - show reservations and receipts
              if (hasInventoryPermission("VIEW_INVENTORY_DIASPORA")) {
                return ["Diaspora Reservations", "Diaspora Receipts"].includes(
                  subItem.title
                );
              }
              return false;
            });
          }
          return true;

        case "Reports":
          return hasInventoryPermission("VIEW_INVENTORY_REPORTS");

        case "Pricing":
          return hasInventoryPermission("VIEW_INVENTORY_PRICING");

        case "Mpesa Transactions":
          return hasInventoryPermission("VIEW_INVENTORY_MPESA_TRANSACTIONS");

        case "Inventory Logs":
          return hasInventoryPermission("VIEW_INVENTORY_LOGS");

        default:
          return true; // Allow other items by default (Dashboard)
      }
    });
  };

  // Filter performance navigation items based on permissions
  const filterPerformanceItems = (items: any[]) => {
    return items.filter((item) => {
      // Check specific performance permissions based on user role
      switch (item.title) {
        // Marketer sections - only visible to marketers (7113)
        case "Marketer Targets":
        case "Marketer Commissions":
        case "Cash On Cash":
        case "Portfolio":
          return hasPerformancePermission("VIEW_PERFORMANCE_MARKETER");

        // GM sections - visible to GM HQ (7114) and GM Karen (7115)
        case "Marketer Performance":
          return (
            hasPerformancePermission("VIEW_PERFORMANCE_GM_HQ") ||
            hasPerformancePermission("VIEW_PERFORMANCE_GM_KAREN") ||
            hasPerformancePermission("VIEW_PERFORMANCE_TEAM_LEADER")
          );

        // Team Leader sections - visible to team leaders (7123) and above
        case "Office Performance":
        case "Targets":
        case "Commissions":
        case "Team Performance":
        case "Profiles":
          return (
            hasPerformancePermission("VIEW_PERFORMANCE_TEAM_LEADER") ||
            hasPerformancePermission("VIEW_PERFORMANCE_GM_HQ") ||
            hasPerformancePermission("VIEW_PERFORMANCE_GM_KAREN")
          );

        default:
          return false; // Hide items that don't match any permission
      }
    });
  };

  // Filter reports navigation items based on permissions
  const filterReportsItems = (items: any[]) => {
    return items.filter((item) => {
      // Check specific report permissions
      switch (item.url) {
        case "/logistics-reports":
          return hasLogisticsPermission("ACCESS_LOGISTICS_REPORTS");
        default:
          return true; // Allow other items by default
      }
    });
  };

  // Create filtered navigation data
  const filteredNavData = {
    ...navData,
    navMain: navData.navMain
      .map((item) => {
        if (item.title === "Logistics") {
          return {
            ...item,
            items: filterLogisticsItems(item.items || []),
          };
        }
        if (item.title === "Inventory") {
          return {
            ...item,
            items: filterInventoryItems(item.items || []),
          };
        }
        return item;
      })
      .filter((item) => {
        // Hide entire Inventory section if user has no inventory permissions
        if (item.title === "Inventory") {
          return hasAnyInventoryPermission();
        }
        return true;
      }),
    NavReports: filterReportsItems(navData.NavReports || []),
    NavPerformance: filterPerformanceItems(navData.NavPerformance || []),
  };

  type NavDataKey = Exclude<keyof typeof navData, "teams">;
  const allSections: Array<{
    label: string;
    itemsKey: NavDataKey;
    permissionKey: SidebarPermissionKey;
  }> = [
    { label: "Main", itemsKey: "navMain", permissionKey: "MAIN" },
    {
      label: "Performance",
      itemsKey: "NavPerformance" as NavDataKey,
      permissionKey: "PERFORMANCE" as SidebarPermissionKey,
    },
    {
      label: "Teams",
      itemsKey: "NavTeams" as NavDataKey,
      permissionKey: "PERFORMANCE" as SidebarPermissionKey,
    },
    {
      label: "Departments",
      itemsKey: "NavDepartments" as NavDataKey,
      permissionKey: "TEAMS" as SidebarPermissionKey,
    },
    // {
    //   label: "Reports",
    //   itemsKey: "NavReports" as NavDataKey,
    //   permissionKey: "REPORTS" as SidebarPermissionKey,
    // },
    // {
    //   label: "Analytics",
    //   itemsKey: "NavAnalytics" as NavDataKey,
    //   permissionKey: "ANALYTICS" as SidebarPermissionKey,
    // },
    { label: "Services", itemsKey: "NavServices", permissionKey: "SERVICES" },
    { label: "Administration", itemsKey: "navAdmin", permissionKey: "ADMIN" },
  ];

  // Filter sections based on user permissions
  const sections = allSections.filter((section) =>
    hasSidebarAccess(section.permissionKey)
  );

  // Fallback: If no sections are accessible, at least show Main section
  const accessibleSections = sections.length > 0 ? sections : [allSections[0]];

  return (
    <Sidebar
      collapsible="icon"
      className="bg-gray-100 text-gray-900 dark:bg-gray-900 dark:text-gray-100"
      {...props}
    >
      <SidebarHeader className="border-b border-gray-300 dark:border-gray-700 sticky top-0 z-10">
        <TeamSwitcher teams={navData.teams} />
      </SidebarHeader>

      <SidebarContent>
        {loadingPermissions ? (
          <div className="flex items-center justify-center p-4">
            <div className="text-sm text-gray-500">Loading permissions...</div>
          </div>
        ) : accessibleSections.length === 0 ? (
          <div className="flex items-center justify-center p-4">
            <div className="text-sm text-gray-500">No accessible sections</div>
          </div>
        ) : (
          accessibleSections.map(({ label, itemsKey }) => (
            <NavMain
              key={label}
              label={label}
              items={filteredNavData[itemsKey]}
              open={levelOne === label}
              onToggle={() => {
                setLevelOne(levelOne === label ? null : label);
                // setOpenSection((prev) => (prev === label ? null : label))
              }}
            />
          ))
        )}
      </SidebarContent>

      {/* <SidebarFooter className="border-t border-gray-300 dark:border-gray-700 p-4">
        <div className="flex items-center justify-between">
          <span className="text-sm font-medium text-sidebar-foreground">
            Theme
          </span>
          <div className="flex items-center space-x-2">
            <Sun className={`w-4 h-4 transition-colors duration-300 ${
              theme === "light"
                ? "text-yellow-500"
                : "text-gray-400 dark:text-gray-500"
            }`} />
            <button
              onClick={toggleTheme}
              className="
                relative inline-flex items-center h-5 w-10
                rounded-full transition-all duration-300 ease-in-out
                bg-gray-200 dark:bg-gray-600
                hover:bg-gray-300 dark:hover:bg-gray-500
                focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-1
                focus:ring-offset-sidebar-background
                border border-gray-300 dark:border-gray-500
              "
              aria-label={`Switch to ${theme === "light" ? "dark" : "light"} mode`}
              title={`Switch to ${theme === "light" ? "dark" : "light"} mode`}
            >
              <span
                className={`${
                  theme === "dark" ? "translate-x-5" : "translate-x-0.5"
                } inline-block w-4 h-4 transform transition-transform duration-300 ease-in-out
                bg-white rounded-full shadow-md border border-gray-200 dark:border-gray-300`}
              />
            </button>
            <Moon className={`w-4 h-4 transition-colors duration-300 ${
              theme === "dark"
                ? "text-blue-400"
                : "text-gray-400"
            }`} />
          </div>
        </div>
      </SidebarFooter> */}
      <SidebarFooter>
        <NavUser />
      </SidebarFooter>
    </Sidebar>
  );
}
