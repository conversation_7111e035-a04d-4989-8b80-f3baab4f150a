# Diaspora Roles Implementation Summary

This document summarizes the implementation of new diaspora roles and updated marketer permissions in the inventory module.

## New Permission Codes Created

| Permission Code | Role | Description |
|-----------------|------|-------------|
| 7123 | Diaspora Regional Manager (DRM) | Access to Diaspora Trips only |
| 7124 | Diaspora Manager (Kasaya) | Access to All Diaspora Trips |

## Updated Permission Structure

### Marketer Permission 7117 (Updated)
**Under My Booking section, marketers now see:**
- My Bookings
- Reservations  
- Diaspora Bookings

**Under Diaspora section, marketers see:**
- Diaspora Reservations
- Diaspora Receipts

### Diaspora Regional Manager 7123 (DRM)
**Under Diaspora section, DRM sees:**
- Trips (only)

### Diaspora Manager Kasaya 7124
**Under Diaspora section, Diaspora Manager sees:**
- All Trips (only)

## Files Modified

### 1. Permission Constants
**File:** `src/hooks/useSidebarPermissions.ts`
- Added `VIEW_INVENTORY_DIASPORA_REGIONAL_MANAGER: 7123`
- Added `VIEW_INVENTORY_DIASPORA_MANAGER_KASAYA: 7124`

### 2. Permission Descriptions  
**File:** `src/utils/permissionChecker.ts`
- Updated 7113 description to include new My Booking structure
- Updated 7117 description for marketer diaspora access
- Added 7123 description for Diaspora Regional Manager
- Added 7124 description for Diaspora Manager Kasaya

### 3. Sidebar Structure
**File:** `src/app-components/sidebar/app-sidebar.tsx`

**My Booking section updated:**
```javascript
{
  title: "My Booking",
  items: [
    { title: "My Bookings", url: "/mybookings#ALL" },
    { title: "Reservations", url: "/diaspora-reservations" },
    { title: "Diaspora Bookings", url: "/mybookings#DIASPORA" },
    { title: "Special Booking", url: "/mybookings#SPECIAL" },
    { title: "Mpesa Booking", url: "/mybookings#MPESA" },
    { title: "Other Booking", url: "/mybookings#OTHER" },
  ],
}
```

**Diaspora section updated:**
```javascript
{
  title: "Diaspora",
  items: [
    { title: "Trips", url: "/diaspora-trips" },
    { title: "All Trips", url: "/diaspora-trips/all" },
    { title: "Diaspora Reservations", url: "/diaspora-reservations" },
    { title: "Diaspora Receipts", url: "/diaspora-receipts" },
  ],
}
```

### 4. Filtering Logic
**File:** `src/app-components/sidebar/app-sidebar.tsx`

**My Booking filtering:**
- Marketers (7117): See "My Bookings", "Reservations", "Diaspora Bookings"
- Other Marketers (7113): See "My Bookings", "Special Booking", "Mpesa Booking", "Other Booking"

**Diaspora filtering:**
- DRM (7123): See only "Trips"
- Diaspora Manager Kasaya (7124): See only "All Trips"  
- Marketers (7117): See "Diaspora Reservations", "Diaspora Receipts"

### 5. Route Protection
**Files:** 
- `src/components/auth/PermissionGuard.tsx`
- `src/hooks/usePermissionNavigation.ts`
- `src/components/navigation/PermissionAwareLink.tsx`

**Updated routes:**
```javascript
'/diaspora-trips': [7117, 7123, 7124], // All diaspora roles
'/diaspora-trips/all': [7124], // Diaspora Manager Kasaya only
'/diaspora-reservations': [7117], // Marketers only
'/diaspora-receipts': [7117], // Marketers only
```

### 6. Documentation
**File:** `docs/INVENTORY_PERMISSIONS.md`
- Added new permission codes to the table
- Updated sidebar access rules
- Added detailed descriptions for new diaspora roles
- Updated route permissions documentation

## Permission Assignment Guide

To assign these permissions to users:

1. **Marketers**: Assign permission 7117 (for My Booking access with reservations)
2. **Diaspora Regional Manager**: Assign permission 7123 (for Trips access)
3. **Diaspora Manager Kasaya**: Assign permission 7124 (for All Trips access)

## Testing Checklist

- [ ] Marketers with 7117 see correct My Booking items
- [ ] Marketers with 7117 see correct Diaspora items  
- [ ] DRM with 7123 sees only Trips in Diaspora section
- [ ] Diaspora Manager with 7124 sees only All Trips in Diaspora section
- [ ] Route access is properly restricted based on permissions
- [ ] Users without permissions cannot access restricted routes

## Notes

- Permission 7123 was previously used for team leaders but has been reassigned to Diaspora Regional Manager
- The implementation maintains backward compatibility with existing marketer permissions
- All route-level protections are in place to prevent unauthorized access
- The sidebar filtering ensures users only see sections they have access to
