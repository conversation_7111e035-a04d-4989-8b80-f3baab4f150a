import React, { useState } from 'react';
import { Screen } from '@/app-components/layout/screen';
import './animations.css';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { 
  DollarSign, 
  User, 
  Users, 
  Building2, 
  Calendar, 
  Search, 
  Filter,
  Eye,
  CheckCircle,
  Clock,
  AlertCircle,
  CreditCard,
  RefreshCw,
  Download,
  TrendingUp,
  TrendingDown,
  Banknote
} from 'lucide-react';
import {
  useGetCreditsTeamIndexQuery
} from '@/redux/slices/teams';
import SpinnerTemp from '@/components/custom/spinners/SpinnerTemp';
import CreditOfficerDetailModal from './CreditOfficerDetailModal';
import { formatNumberWithCommas } from '@/utils/salesDataFormatter';
import {
  CreditOfficerType,
  CreditsTeamIndexType
} from '@/types/creditsTeam';

const CreditsTeamDashboard: React.FC = () => {
  const [selectedCreditOfficer, setSelectedCreditOfficer] = useState<CreditOfficerType | null>(null);
  const [isDetailModalOpen, setIsDetailModalOpen] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');

  const {
    data: creditsData,
    isLoading,
    error,
    refetch
  } = useGetCreditsTeamIndexQuery() as {
    data: CreditsTeamIndexType | undefined;
    isLoading: boolean;
    error: any;
    refetch: () => void;
  };

  const handleCardClick = (creditOfficer: CreditOfficerType) => {
    setSelectedCreditOfficer(creditOfficer);
    setIsDetailModalOpen(true);
  };

  const handleRefresh = () => {
    refetch();
  };

  // Filter credit officers based on search term
  const filteredCreditOfficers = React.useMemo(() => {
    if (!creditsData?.Credits_Teams_Performance) return [];
    
    return creditsData.Credits_Teams_Performance.filter(officer =>
      officer.fullnames.toLowerCase().includes(searchTerm.toLowerCase()) ||
      officer.credit_officer_id.toLowerCase().includes(searchTerm.toLowerCase())
    );
  }, [creditsData?.Credits_Teams_Performance, searchTerm]);

  const getCreditOfficerIcon = () => {
    return <User className="w-5 h-5 text-blue-600 dark:text-blue-400" />;
  };

  if (isLoading) {
    return (
      <Screen>
        <div className="flex justify-center items-center min-h-[400px]">
          <SpinnerTemp type="spinner-double" size="lg" />
        </div>
      </Screen>
    );
  }

  if (error) {
    return (
      <Screen>
        <div className="flex flex-col items-center justify-center min-h-[400px] space-y-4">
          <AlertCircle className="w-12 h-12 text-red-500" />
          <h2 className="text-xl font-semibold text-gray-900 dark:text-white">Error Loading Dashboard</h2>
          <p className="text-gray-600 dark:text-gray-400 text-center max-w-md">
            Unable to load credits team data. Please try again.
          </p>
          <Button onClick={handleRefresh} variant="outline">
            <RefreshCw className="w-4 h-4 mr-2" />
            Retry
          </Button>
        </div>
      </Screen>
    );
  }

  return (
    <Screen>
      <div className="space-y-8">
        {/* Professional Header Section */}
        <div className="bg-white dark:bg-gray-900 border-b border-gray-200 dark:border-gray-700 py-6">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <div className="p-2 bg-green-50 dark:bg-green-900/20 rounded-lg">
                <CreditCard className="w-6 h-6 text-green-600 dark:text-green-400" />
              </div>
              <div>
                <h1 className="text-2xl font-bold text-gray-900 dark:text-white">Credits Team Dashboard</h1>
                <p className="text-gray-600 dark:text-gray-400 text-sm">
                  Manage and track credit officers performance • {creditsData?.Credits_Teams_Performance?.length || 0} credit officers
                </p>
              </div>
            </div>
            <div className="flex items-center space-x-3">
              <Button
                variant="outline"
                size="sm"
                onClick={handleRefresh}
                className="hover:bg-gray-50 dark:hover:bg-gray-800"
              >
                <RefreshCw className="w-4 h-4 mr-2" />
                Refresh
              </Button>
            </div>
          </div>
        </div>

        {/* Collections Overview Cards */}
        {creditsData?.Collections && (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            <Card className="border-l-4 border-l-green-500 bg-gradient-to-br from-green-50 to-green-100 dark:from-green-900/20 dark:to-green-800/20">
              <CardContent className="pt-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm text-gray-600 dark:text-gray-400">All Overdue Collections</p>
                    <p className="text-2xl font-bold text-gray-900 dark:text-white">
                      KES {formatNumberWithCommas(creditsData.Collections.ALL_Overdue_Collections)}
                    </p>
                  </div>
                  <Banknote className="w-8 h-8 text-green-500" />
                </div>
              </CardContent>
            </Card>

            <Card className="border-l-4 border-l-blue-500 bg-gradient-to-br from-blue-50 to-blue-100 dark:from-blue-900/20 dark:to-blue-800/20">
              <CardContent className="pt-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm text-gray-600 dark:text-gray-400">Collections Collected</p>
                    <p className="text-2xl font-bold text-gray-900 dark:text-white">
                      KES {formatNumberWithCommas(creditsData.Collections.Overdue_Collections_Collected)}
                    </p>
                  </div>
                  <TrendingUp className="w-8 h-8 text-blue-500" />
                </div>
              </CardContent>
            </Card>

            <Card className="border-l-4 border-l-amber-500 bg-gradient-to-br from-amber-50 to-amber-100 dark:from-amber-900/20 dark:to-amber-800/20">
              <CardContent className="pt-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm text-gray-600 dark:text-gray-400">Installments Due Today</p>
                    <p className="text-2xl font-bold text-gray-900 dark:text-white">
                      {formatNumberWithCommas(creditsData.Collections.Installments_Due_Today)}
                    </p>
                  </div>
                  <Calendar className="w-8 h-8 text-amber-500" />
                </div>
              </CardContent>
            </Card>

            <Card className="border-l-4 border-l-red-500 bg-gradient-to-br from-red-50 to-red-100 dark:from-red-900/20 dark:to-red-800/20">
              <CardContent className="pt-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm text-gray-600 dark:text-gray-400">Below Threshold</p>
                    <p className="text-2xl font-bold text-gray-900 dark:text-white">
                      {formatNumberWithCommas(creditsData.Collections.Sales_Deposits_Below_Threshold)}
                    </p>
                  </div>
                  <TrendingDown className="w-8 h-8 text-red-500" />
                </div>
              </CardContent>
            </Card>
          </div>
        )}

        {/* Period Information */}
        {creditsData?.Current_Month && (
          <Card className="bg-gradient-to-r from-indigo-50 to-purple-50 dark:from-indigo-900/20 dark:to-purple-900/20 border-indigo-200 dark:border-indigo-800">
            <CardContent className="pt-6">
              <div className="flex items-center justify-center space-x-8">
                <div className="text-center">
                  <p className="text-sm text-gray-600 dark:text-gray-400">Current Period</p>
                  <p className="text-lg font-semibold text-gray-900 dark:text-white">
                    {creditsData.Current_Month.Period_Start_Date} to {creditsData.Current_Month.Period_End_Date}
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>
        )}

        {/* Search and Filter Section */}
        <div className="flex flex-col sm:flex-row gap-4 items-center justify-between">
          <div className="relative flex-1 max-w-md">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
            <Input
              placeholder="Search credit officers..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-10 bg-white dark:bg-gray-800 border-gray-200 dark:border-gray-700"
            />
          </div>
          <div className="flex items-center space-x-2 text-sm text-gray-600 dark:text-gray-400">
            <Users className="w-4 h-4" />
            <span>{filteredCreditOfficers.length} credit officers</span>
          </div>
        </div>

        {/* Credit Officers Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
          {filteredCreditOfficers.map((creditOfficer, index) => (
            <Card
              key={`${creditOfficer.credit_officer_id}-${index}`}
              className="group hover:shadow-lg transition-all duration-200 cursor-pointer border border-gray-200 dark:border-gray-700 bg-white dark:bg-gray-800 hover:border-green-300 dark:hover:border-green-600 animate-fade-in-up"
              style={{ animationDelay: `${index * 50}ms` }}
              onClick={() => handleCardClick(creditOfficer)}
            >
              <CardHeader className="pb-3">
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-2">
                    {getCreditOfficerIcon()}
                    <Badge variant="secondary" className="text-xs">
                      Credit Officer
                    </Badge>
                  </div>
                  <Button
                    variant="ghost"
                    size="sm"
                    className="opacity-0 group-hover:opacity-100 transition-opacity"
                  >
                    <Eye className="w-4 h-4" />
                  </Button>
                </div>
                <CardTitle className="text-lg font-semibold text-gray-900 dark:text-white group-hover:text-green-600 dark:group-hover:text-green-400 transition-colors">
                  {creditOfficer.fullnames}
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  <div className="flex items-center justify-between text-sm">
                    <span className="text-gray-600 dark:text-gray-400">Officer ID:</span>
                    <span className="font-medium text-gray-900 dark:text-white">
                      {creditOfficer.credit_officer_id}
                    </span>
                  </div>
                  <div className="pt-2 border-t border-gray-100 dark:border-gray-700">
                    <Button
                      variant="outline"
                      size="sm"
                      className="w-full group-hover:bg-green-50 group-hover:border-green-200 dark:group-hover:bg-green-900/20 dark:group-hover:border-green-800"
                    >
                      <Eye className="w-4 h-4 mr-2" />
                      View Details
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* No Results Message */}
        {filteredCreditOfficers.length === 0 && searchTerm && (
          <div className="text-center py-12">
            <Search className="w-12 h-12 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">No credit officers found</h3>
            <p className="text-gray-600 dark:text-gray-400">
              Try adjusting your search terms or clear the search to see all credit officers.
            </p>
          </div>
        )}

        {/* Detail Modal */}
        {selectedCreditOfficer && (
          <CreditOfficerDetailModal
            isOpen={isDetailModalOpen}
            onClose={() => {
              setIsDetailModalOpen(false);
              setSelectedCreditOfficer(null);
            }}
            creditOfficer={selectedCreditOfficer}
          />
        )}
      </div>
    </Screen>
  );
};

export default CreditsTeamDashboard;
